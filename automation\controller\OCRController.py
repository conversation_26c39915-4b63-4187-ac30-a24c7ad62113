import time
from typing import List
import win32gui

from automation.controller.BaseController import BaseController
from automation.services.OCRService import OCRService
from automation.myutils.timerutils import RateLimiter
from automation.myutils.configutils import DebugConfig
from mylogger.MyLogger import MyLogger

logger = MyLogger('OCRController')

class OCRResult:
    def __init__(self, corner,text,percent):
        self.corner = corner
        self.text = text
        self.percent = percent
        self.center = self.get_line_center(corner)

    def get_line_center(self, rect):
        """
        获取行文字的中心点坐标
        :param rect:
        :return:
        """
        left_top = rect[0]
        right_top = rect[1]
        right_bottom = rect[2]
        left_bottom = rect[3]
        # print(left_top, right_top, right_bottom, left_bottom)

        center_x = (left_top[0] + right_top[0]) / 2
        center_y = (left_top[1] + left_bottom[1]) / 2
        return center_x, center_y

    def __str__(self):
        return str(self.text)

# @cache
class OCRController(BaseController):
    def __init__(self,debug_enable=True,gc=None):
        if debug_enable is None: debug_enable = DebugConfig.get(DebugConfig.KEY_DEBUG_ENABLE, False)
        self.debug_enable = debug_enable
        super().__init__(debug_enable, gc)
        self.ocr_result = []
        self.ocr_update_limiter = RateLimiter(0.2)

        # 延迟获取窗口句柄，每次使用时验证
        self.gc = gc;

        # 创建OCRService实例，传入gc
        self.ocr_service = OCRService()

    def _get_valid_hwnd(self):
        """获取有效的窗口句柄，每次使用时验证"""
        try:
            # 如果capture对象有刷新方法，先调用刷新
            if hasattr(self.gc, '_refresh_window_handle'):
                self.gc._refresh_window_handle()

            # 获取最新的句柄
            if hasattr(self.gc, 'hwnd') and self.gc.hwnd:
                # 验证句柄是否仍然有效
                if win32gui.IsWindow(self.gc.hwnd):
                    return self.gc.hwnd
                else:
                    logger.warning("窗口句柄已失效，尝试重新获取")
                    # 触发重新获取句柄
                    if hasattr(self.gc, '_update_rect'):
                        self.gc._update_rect()
                        return self.gc.hwnd

            return None
        except Exception as e:
            logger.error(f"获取窗口句柄失败: {e}")
            return None

    @property
    def hwnd(self):
        """动态获取窗口句柄"""
        return self._get_valid_hwnd()

    def get_ocr_result(self, mss_mode=False)->List[OCRResult]:
        """获取当前绑定窗口的OCR结果"""
        if mss_mode: 
            self._update_ocr_result(mss_mode=mss_mode)
        else: 
            self.ocr_update_limiter.execute(self._update_ocr_result)
        return self.ocr_result

    def _update_ocr_result(self, mss_mode=False):
        """
        OCR是一比巨大的开销，添加时间间隔防止调用卡顿
        :return:
        """

        if mss_mode:
            self.log("使用MSS模式获取OCR结果")
            image = self.gc.get_screenshot(mss_mode=True)
        else:
            self.log("使用默认模式获取OCR结果")
            image = self.gc.get_screenshot()

        result = self.ocr_service.recognize_text(image)

        if not result: return

        self.log(f"ocr result: {result}")

        # 处理新的PaddleOCR格式
        self.ocr_result = []
        
        # 新格式：result是一个列表，取第一个元素
        if isinstance(result, list) and len(result) > 0:
            res_data = result[0]
        else:
            res_data = result
            
        rec_texts = res_data.get('rec_texts', [])
        rec_scores = res_data.get('rec_scores', [])
        rec_polys = res_data.get('rec_polys', [])
        
        # 组合文本、置信度和坐标
        for i, text in enumerate(rec_texts):
            if text.strip() and i < len(rec_scores) and i < len(rec_polys):
                ocr_result_obj = OCRResult(
                    corner=rec_polys[i].tolist(),  # 转换numpy数组为列表
                    text=text,
                    percent=float(rec_scores[i])
                )
                self.ocr_result.append(ocr_result_obj)


    def get_line_center(self, rect):
        """
        获取行文字的中心点坐标
        :param rect:
        :return:
        """
        left_top = rect[0]
        right_top = rect[1]
        right_bottom = rect[2]
        left_bottom = rect[3]
        # print(left_top, right_top, right_bottom, left_bottom)

        center_x = (left_top[0] + right_top[0]) / 2
        center_y = (left_top[1] + left_bottom[1]) / 2
        return center_x, center_y

    def find_match_text(self, target_text, match_all=False, mss_mode=False)->List[OCRResult]:
        try:
            if self.stop_listen or target_text is None: return []
            self.log(f"正在查找'{target_text}'")
            # img = self.gc.get_screenshot()
            result = self.get_ocr_result(mss_mode=mss_mode)
            match_texts:List[OCRResult] = []
            for item in result:
                if target_text in item.text:
                    if match_all and target_text != item.text: continue
                    match_texts.append(item)
            return match_texts
        except Exception as e:
            logger.error(e.args)

    def find_text_and_click(self, target_text, match_all=False, index=0, click_all=False, mss_mode=False):
        """
        找到屏幕中匹配的文字并点击
        :param target_text:
        :param match_all: 是否完全匹配文本
        :param index: 出现多个选项时候，点击的索引
        :param click_all: 点击所有匹配的文本，此选项优先于index
        :return:
        """
        match_texts = self.find_match_text(target_text, match_all, mss_mode)

        l = len(match_texts)
        if index < 0 or l == 0: return False

        self.log(f"共发现{l}个匹配的文本")
        if index >= l:
            self.log('你选择的下标超过了匹配数量，自动设置为最后一个')
            index = l - 1

        if click_all:
            self.log(f"点击所有匹配的文本")
            for match in match_texts:
                self.click_ocr_result(match)
                self.log(f"已经执行点击'{target_text}'操作")
        else:
            self.log(f"点击第{index + 1}个匹配的文本")
            match = match_texts[index]
            self.click_ocr_result(match)
            self.log(f"已经执行点击'{target_text}'操作")
        return True

    def find_text_and_bg_click(self, target_text, match_all=False, index=0, 
                              click_all=False, mss_mode=False, button='left'):
        """找到文字并后台点击当前绑定的窗口"""
        match_texts = self.find_match_text(target_text, match_all, mss_mode)
        
        l = len(match_texts)
        if index < 0 or l == 0: return False

        self.log(f"共发现{l}个匹配的文本")
        if index >= l:
            self.log('你选择的下标超过了匹配数量，自动设置为最后一个')
            index = l - 1

        if click_all:
            self.log(f"点击所有匹配的文本")
            for match in match_texts:
                self.bg_click_ocr_result(match, button)
                self.log(f"已经执行点击'{target_text}'操作")
        else:
            self.log(f"点击第{index + 1}个匹配的文本")
            match = match_texts[index]
            self.bg_click_ocr_result(match, button)
            self.log(f"已经执行点击'{target_text}'操作")
        return True

    def click_ocr_result(self, ocr_result):
        """前台点击OCR结果"""
        center_x, center_y = ocr_result.center
        pos = self.gc.get_screen_position((center_x, center_y))
        self.log((center_x, center_y), "->", pos)
        self.set_ms_position(pos)
        self.mouse_left_click()

    def bg_click_ocr_result(self, ocr_result, button='left'):
        """后台点击OCR结果"""
        center_x, center_y = ocr_result.center
        self.bg_click(center_x, center_y, button)

    def is_text_in_screen(self, *args, match_all=False):
        """
        :param args:
        :param match_all: 全文匹配某个指定的文本
        :return:
        """
        if self.stop_listen or args is None: return
        self.log(f"在屏幕中查找{args}")
        result = self.get_ocr_result()
        for item in result:
            for arg in args:
                if match_all:
                    if arg == item.text:
                        self.logger.debug(f"屏幕上出现文本{arg},原文是{item.text}")
                        return True
                else:
                    if arg in item.text:
                        self.logger.debug(f"屏幕上出现文本{arg},原文是{item.text}")
                        return True

        # if result is None: return False
        # for idx in range(len(result)):
        #     res = result[idx]
        #     for line in res:
        #         # if target_text in line[1][0]:
        #         for arg in args:
        #             try:
        #                 if arg is None: return False
        #                 if arg in line[1][0]:
        #                     self.log("屏幕上出现文本{}, 原文是{}:".format(arg, line[1][0]))
        #                     return True
        #             except TypeError as e:
        #                 self.log(line, e)
        #                 return False
        # self.log(f"没有找到{args}")
        return False


if __name__ == '__main__':
    # cv2.imshow('sc', gc.get_screenshot())
    # cv2.waitKey(0)
    ocr = OCRController()
    results = ocr.get_ocr_result()
    for result in results:
        print(result)
    # ocr.find_text_and_click('渊下宫', match_all=True)
    # ocr.is_text_in_screen('探索度')
    # ocr.find_text_and_click("锚点")
    # ocr.find_text_and_click("七天神像")
    # ocr.find_text_and_click("点击空白")
    # ocr.find_text_and_click("传送")
    # ocr.find_text_and_click("钓鱼")
    # ocr.find_text_and_click("UID:")
    # print(ocr.is_text_in_screen("传送锚点"))
    # print(ocr.find_text_and_click("传送锚点"))
    # print(ocr.is_text_in_screen("蔷薇"))
    # while True:
    #     result = ocr.get_ocr_result()
    #     print(result)
        # time.sleep(15)
        # ocr.is_text_in_screen('复苏')
        # matches = ocr.find_match_text('复苏')
        # for match in matches:
        #     center = ocr.get_line_center(match.corner)
        #     ocr.click(center[0], center[1])




















