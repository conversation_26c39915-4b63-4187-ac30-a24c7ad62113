"""
测试句柄解决方案的运行脚本
放在项目根目录，避免导入路径问题
"""
import time
import win32gui
from automation.capture.identify_sandboxie_capture import IdentifySandboxieCapture
from automation.controller.OCRController import OCRController
from mylogger.MyLogger import MyLogger

logger = MyLogger('TestHandleSolution')

# 全局缓存
_cached_capture = None
_cached_ocr = None

def ensure_valid_handle(capture):
    """确保capture的句柄有效，如果无效则刷新"""
    if not capture:
        return False
    
    try:
        hwnd = capture.hwnd
        
        # 检查句柄是否有效
        if hwnd and win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd):
            # 进一步检查窗口标题
            title = win32gui.GetWindowText(hwnd)
            if "sandboxie" in title.lower():
                return True
        
        # 句柄无效，刷新
        logger.info("句柄失效，正在刷新...")
        capture._update_rect()
        
        # 检查刷新后的句柄
        new_hwnd = capture.hwnd
        if new_hwnd and win32gui.IsWindow(new_hwnd):
            logger.info(f"句柄刷新成功: {hwnd} -> {new_hwnd}")
            return True
        else:
            logger.error("句柄刷新失败")
            return False
            
    except Exception as e:
        logger.error(f"句柄验证失败: {e}")
        return False

def get_sandboxie_capture():
    """获取Sandboxie捕获器，自动处理句柄失效"""
    global _cached_capture
    
    if _cached_capture is None:
        logger.info("创建新的Sandboxie捕获器")
        _cached_capture = IdentifySandboxieCapture()
    else:
        # 验证并刷新句柄
        ensure_valid_handle(_cached_capture)
    
    return _cached_capture

def get_ocr_controller(gc=None):
    """获取OCR控制器，自动处理句柄失效"""
    global _cached_ocr, _cached_capture
    
    if gc is None:
        gc = get_sandboxie_capture()
    
    if _cached_ocr is None:
        logger.info("创建新的OCR控制器")
        _cached_ocr = OCRController(gc=gc)
    else:
        # 确保OCR控制器使用的是最新的capture
        _cached_ocr.gc = gc
    
    return _cached_ocr

def your_original_code_fixed():
    """
    你的原始代码的修复版本
    """
    sandboxie_capture = get_sandboxie_capture()  # 自动处理句柄失效
    ocr_controller = get_ocr_controller(gc=sandboxie_capture)  # 自动处理句柄失效
    
    # 使用OCR查找并点击"沙箱"文字
    return ocr_controller.find_text_and_bg_click("沙箱", match_all=False, mss_mode=False)

def click_sandbox_text():
    """一行式调用，完全隐藏复杂性"""
    return get_ocr_controller().find_text_and_bg_click("沙箱", match_all=False, mss_mode=False)

def get_current_handle_info():
    """获取当前句柄信息（用于调试）"""
    global _cached_capture
    if _cached_capture:
        try:
            hwnd = _cached_capture.hwnd
            if hwnd:
                title = win32gui.GetWindowText(hwnd)
                is_valid = win32gui.IsWindow(hwnd)
                is_visible = win32gui.IsWindowVisible(hwnd)
                
                return {
                    'hwnd': hwnd,
                    'title': title,
                    'is_valid': is_valid,
                    'is_visible': is_visible
                }
        except Exception as e:
            return {'error': str(e)}
    return {'status': 'no_instance'}

def test_multiple_calls():
    """测试多次调用"""
    print("=== 测试多次调用 ===")
    
    for i in range(3):
        print(f"\n--- 第{i+1}次调用 ---")
        
        # 获取实例
        capture = get_sandboxie_capture()
        ocr = get_ocr_controller()
        
        # 显示信息
        info = get_current_handle_info()
        print(f"句柄: {info.get('hwnd', 'None')}")
        print(f"标题: {info.get('title', 'None')}")
        print(f"capture对象ID: {id(capture)}")
        print(f"ocr对象ID: {id(ocr)}")
        
        # 执行操作
        try:
            success = ocr.find_text_and_bg_click("沙箱", match_all=False, mss_mode=False)
            print(f"操作结果: {success}")
        except Exception as e:
            print(f"操作失败: {e}")
        
        time.sleep(1)

def test_handle_refresh():
    """测试句柄刷新机制"""
    print("=== 测试句柄刷新机制 ===")
    
    # 第一次获取
    capture1 = get_sandboxie_capture()
    hwnd1 = capture1.hwnd
    print(f"第一次获取句柄: {hwnd1}")
    
    # 强制刷新句柄
    print("\n强制刷新句柄...")
    if hasattr(capture1, '_update_rect'):
        capture1._update_rect()
        hwnd2 = capture1.hwnd
        print(f"刷新后句柄: {hwnd2}")
        print(f"句柄是否变化: {hwnd1 != hwnd2}")
    
    # 再次获取，应该使用相同的实例
    capture2 = get_sandboxie_capture()
    print(f"第二次获取，实例相同: {capture1 is capture2}")
    print(f"当前句柄: {capture2.hwnd}")

if __name__ == "__main__":
    print("=== 句柄解决方案测试 ===")
    
    try:
        # 测试1：基本功能
        print("\n1. 基本功能测试:")
        result = your_original_code_fixed()
        print(f"结果: {result}")
        
        # 测试2：多次调用
        print("\n2. 多次调用测试:")
        test_multiple_calls()
        
        # 测试3：句柄刷新
        print("\n3. 句柄刷新测试:")
        test_handle_refresh()
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
