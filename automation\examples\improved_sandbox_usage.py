"""
改进后的Sandboxie自动化使用示例
解决了单例模式下窗口句柄失效的问题
"""
from automation.managers.capture_manager import get_sandboxie_automation, capture_manager
from mylogger.MyLogger import MyLogger

logger = MyLogger('SandboxieAutomation')

def click_sandbox_text_improved():
    """改进后的沙箱文本点击方法"""
    try:
        # 使用管理器获取实例，自动处理句柄失效问题
        sandboxie_capture, ocr_controller = get_sandboxie_automation()
        
        # 使用OCR查找并点击"沙箱"文字
        success = ocr_controller.find_text_and_bg_click("沙箱", match_all=False, mss_mode=False)
        
        if success:
            logger.info("成功点击沙箱文本")
        else:
            logger.warning("未找到沙箱文本")
            
        return success
        
    except Exception as e:
        logger.error(f"点击沙箱文本失败: {e}")
        return False

def multiple_operations_example():
    """演示多次操作的使用方式"""
    
    # 第一次操作
    logger.info("=== 第一次操作 ===")
    click_sandbox_text_improved()
    
    # 模拟窗口可能被关闭/重新打开的情况
    logger.info("=== 模拟窗口状态变化 ===")
    
    # 强制刷新所有capture（模拟窗口重新打开）
    capture_manager.refresh_all_captures()
    
    # 第二次操作 - 应该能正常工作
    logger.info("=== 第二次操作 ===")
    click_sandbox_text_improved()
    
    # 查看当前状态
    status = capture_manager.get_capture_status()
    logger.info(f"当前capture状态: {status}")

def batch_operations_example():
    """批量操作示例"""
    operations = [
        ("沙箱", False, False),
        ("创建", False, False), 
        ("启动", False, False),
        ("停止", False, False)
    ]
    
    # 获取一次实例，重复使用
    sandboxie_capture, ocr_controller = get_sandboxie_automation()
    
    for text, match_all, mss_mode in operations:
        try:
            logger.info(f"查找并点击文本: {text}")
            success = ocr_controller.find_text_and_bg_click(text, match_all, mss_mode)
            
            if success:
                logger.info(f"成功点击: {text}")
            else:
                logger.warning(f"未找到文本: {text}")
                
        except Exception as e:
            logger.error(f"操作失败 {text}: {e}")

# 原始代码的改进版本
def original_code_improved():
    """你原始代码的改进版本"""
    try:
        # 1. 获取Sandboxie捕获器（自动处理单例和句柄刷新）
        sandboxie_capture, ocr_controller = get_sandboxie_automation()
        
        # 2. 使用OCR查找并点击"沙箱"文字（句柄自动验证）
        success = ocr_controller.find_text_and_bg_click("沙箱", match_all=False, mss_mode=False)
        
        if success:
            logger.info("操作成功完成")
        else:
            logger.warning("未找到目标文本")
            
        return success
        
    except Exception as e:
        logger.error(f"自动化操作失败: {e}")
        return False

if __name__ == "__main__":
    # 测试各种使用场景
    
    print("1. 基本使用示例")
    original_code_improved()
    
    print("\n2. 多次操作示例")
    multiple_operations_example()
    
    print("\n3. 批量操作示例")
    batch_operations_example()
