"""
窗口捕获管理器 - 解决单例模式下窗口句柄失效问题
"""
import threading
import time
import win32gui
from typing import Dict, Optional
from mylogger.MyLogger import MyLogger
from automation.capture.identify_sandboxie_capture import IdentifySandboxieCapture
from automation.controller.OCRController import OCRController

logger = MyLogger('CaptureManager')

class CaptureManager:
    """
    窗口捕获管理器 - 单例模式
    负责管理所有capture实例，自动处理窗口句柄失效问题
    """
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._captures: Dict[str, object] = {}
        self._ocr_controllers: Dict[str, OCRController] = {}
        self._last_validation_time: Dict[str, float] = {}
        self._validation_interval = 5.0  # 5秒验证一次
        
    def get_sandboxie_capture(self, window_name="Sandboxie-Plus") -> IdentifySandboxieCapture:
        """获取Sandboxie捕获器实例"""
        key = f"sandboxie_{window_name}"
        
        if key not in self._captures:
            logger.info(f"创建新的Sandboxie捕获器: {window_name}")
            self._captures[key] = IdentifySandboxieCapture(window_name)
            self._last_validation_time[key] = time.time()
        else:
            # 检查是否需要验证窗口句柄
            if self._should_validate(key):
                self._validate_capture(key)
        
        return self._captures[key]
    
    def get_ocr_controller(self, capture_key="sandboxie_Sandboxie-Plus") -> OCRController:
        """获取OCR控制器实例"""
        if capture_key not in self._ocr_controllers:
            # 获取对应的capture实例
            if capture_key.startswith("sandboxie_"):
                window_name = capture_key.replace("sandboxie_", "")
                capture = self.get_sandboxie_capture(window_name)
            else:
                raise ValueError(f"不支持的capture类型: {capture_key}")
            
            logger.info(f"创建新的OCR控制器: {capture_key}")
            self._ocr_controllers[capture_key] = OCRController(gc=capture)
        
        return self._ocr_controllers[capture_key]
    
    def _should_validate(self, key: str) -> bool:
        """检查是否需要验证窗口句柄"""
        current_time = time.time()
        last_time = self._last_validation_time.get(key, 0)
        return current_time - last_time > self._validation_interval
    
    def _validate_capture(self, key: str) -> bool:
        """验证并刷新capture的窗口句柄"""
        try:
            capture = self._captures[key]
            
            # 检查窗口句柄是否仍然有效
            if hasattr(capture, 'hwnd') and capture.hwnd:
                if win32gui.IsWindow(capture.hwnd):
                    # 检查窗口标题是否匹配
                    try:
                        current_title = win32gui.GetWindowText(capture.hwnd)
                        if capture.window_name in current_title:
                            logger.debug(f"窗口句柄验证通过: {key}")
                            self._last_validation_time[key] = time.time()
                            return True
                    except:
                        pass
            
            # 句柄无效，重新获取
            logger.warning(f"窗口句柄已失效，重新获取: {key}")
            if hasattr(capture, '_update_rect'):
                capture._update_rect()
                self._last_validation_time[key] = time.time()
                return True
            
        except Exception as e:
            logger.error(f"验证capture失败 {key}: {e}")
            return False
        
        return False
    
    def refresh_all_captures(self):
        """刷新所有capture的窗口句柄"""
        logger.info("刷新所有capture的窗口句柄")
        for key in self._captures.keys():
            self._validate_capture(key)
    
    def clear_cache(self):
        """清除所有缓存的实例"""
        logger.info("清除capture管理器缓存")
        self._captures.clear()
        self._ocr_controllers.clear()
        self._last_validation_time.clear()
    
    def get_capture_status(self) -> Dict[str, dict]:
        """获取所有capture的状态信息"""
        status = {}
        for key, capture in self._captures.items():
            try:
                hwnd_valid = False
                if hasattr(capture, 'hwnd') and capture.hwnd:
                    hwnd_valid = win32gui.IsWindow(capture.hwnd)
                
                status[key] = {
                    'hwnd': getattr(capture, 'hwnd', None),
                    'hwnd_valid': hwnd_valid,
                    'window_name': getattr(capture, 'window_name', 'Unknown'),
                    'last_validation': self._last_validation_time.get(key, 0)
                }
            except Exception as e:
                status[key] = {'error': str(e)}
        
        return status

# 全局实例
capture_manager = CaptureManager()

def get_sandboxie_automation():
    """
    便捷函数：获取Sandboxie自动化所需的capture和ocr实例
    返回: (capture, ocr_controller)
    """
    capture = capture_manager.get_sandboxie_capture()
    ocr_controller = capture_manager.get_ocr_controller()
    return capture, ocr_controller

# 使用示例
if __name__ == "__main__":
    # 测试用法
    manager = CaptureManager()
    
    # 获取capture和ocr
    capture, ocr = get_sandboxie_automation()
    
    # 查看状态
    status = manager.get_capture_status()
    print("Capture状态:", status)
    
    # 模拟多次调用
    for i in range(3):
        capture2, ocr2 = get_sandboxie_automation()
        print(f"第{i+1}次调用 - capture相同: {capture is capture2}, ocr相同: {ocr is ocr2}")
        time.sleep(1)
