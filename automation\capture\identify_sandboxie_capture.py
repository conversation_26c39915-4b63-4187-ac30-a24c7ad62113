import cv2
import os.path
import win32gui
from automation.capture.sandboxie_capture import Sand<PERSON>ie<PERSON>apture
from automation.myutils.configutils import resource_path
from mylogger.MyLogger import MyLogger

logger = MyLogger('IdentifySandboxieCapture')

class IdentifySandboxieCapture(SandboxieCapture):
    """Sandboxie界面识别捕获器 - 专注于模板匹配"""

    _instance = None
    _initialized = False

    def __new__(cls, window_name="Sandboxie-Plus"):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, window_name="Sandboxie-Plus"):
        if not self._initialized:
            super().__init__(window_name)
            self._load_sandboxie_templates()
            self._initialized = True
        else:
            # 重新验证和更新窗口句柄
            self._refresh_window_handle()

    def _refresh_window_handle(self):
        """刷新窗口句柄，确保有效性"""
        try:
            # 检查当前句柄是否仍然有效
            if self.hwnd and win32gui.IsWindow(self.hwnd):
                # 检查窗口是否仍然存在且标题匹配
                current_title = win32gui.GetWindowText(self.hwnd)
                if self.window_name in current_title:
                    logger.debug(f"窗口句柄仍然有效: {self.hwnd}")
                    return True

            # 句柄无效，重新获取
            logger.info("窗口句柄已失效，重新获取...")
            self._update_rect()
            return True

        except Exception as e:
            logger.error(f"刷新窗口句柄失败: {e}")
            return False
    
    def _load_sandboxie_templates(self):
        """加载Sandboxie界面模板"""
        template_path = os.path.join(resource_path, 'template', 'sandboxie')
        
    #     self.icon_sandbox_create = cv2.imread(os.path.join(template_path, 'btn_create_sandbox.png'), cv2.IMREAD_GRAYSCALE)
    #     self.icon_sandbox_start = cv2.imread(os.path.join(template_path, 'btn_start_sandbox.png'), cv2.IMREAD_GRAYSCALE)
    #     self.icon_sandbox_stop = cv2.imread(os.path.join(template_path, 'btn_stop_sandbox.png'), cv2.IMREAD_GRAYSCALE)
    
    # def has_create_button(self):
    #     """检测是否有创建沙盒按钮"""
    #     return self.has_template_icon_in_screen(self.icon_sandbox_create)
    
    # def has_start_button(self):
    #     """检测是否有启动按钮"""
    #     return self.has_template_icon_in_screen(self.icon_sandbox_start)
        
    # def find_sandbox_by_name(self, sandbox_name):
    #     """通过名称查找沙盒位置"""
    #     # OCR识别沙盒列表中的文字
    #     pass
