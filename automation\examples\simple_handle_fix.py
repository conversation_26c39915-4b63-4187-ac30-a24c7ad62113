"""
最简单的句柄失效解决方案
直接替换你的三行代码，保持使用方式不变
"""
import sys
import os
import win32gui

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from automation.capture.identify_sandboxie_capture import IdentifySandboxieCapture
from automation.controller.OCRController import OCRController
from mylogger.MyLogger import MyLogger

logger = MyLogger('SimpleHandleFix')

# 全局缓存
_cached_capture = None
_cached_ocr = None

def ensure_valid_handle(capture):
    """确保capture的句柄有效，如果无效则刷新"""
    if not capture:
        return False
    
    try:
        hwnd = capture.hwnd
        
        # 检查句柄是否有效
        if hwnd and win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd):
            # 进一步检查窗口标题
            title = win32gui.GetWindowText(hwnd)
            if "sandboxie" in title.lower():
                return True
        
        # 句柄无效，刷新
        logger.info("句柄失效，正在刷新...")
        capture._update_rect()
        
        # 检查刷新后的句柄
        new_hwnd = capture.hwnd
        if new_hwnd and win32gui.IsWindow(new_hwnd):
            logger.info(f"句柄刷新成功: {hwnd} -> {new_hwnd}")
            return True
        else:
            logger.error("句柄刷新失败")
            return False
            
    except Exception as e:
        logger.error(f"句柄验证失败: {e}")
        return False

def get_sandboxie_capture():
    """获取Sandboxie捕获器，自动处理句柄失效"""
    global _cached_capture
    
    if _cached_capture is None:
        logger.info("创建新的Sandboxie捕获器")
        _cached_capture = IdentifySandboxieCapture()
    else:
        # 验证并刷新句柄
        ensure_valid_handle(_cached_capture)
    
    return _cached_capture

def get_ocr_controller(gc=None):
    """获取OCR控制器，自动处理句柄失效"""
    global _cached_ocr, _cached_capture
    
    if gc is None:
        gc = get_sandboxie_capture()
    
    if _cached_ocr is None:
        logger.info("创建新的OCR控制器")
        _cached_ocr = OCRController(gc=gc)
    else:
        # 确保OCR控制器使用的是最新的capture
        _cached_ocr.gc = gc
    
    return _cached_ocr

def reset_cache():
    """重置缓存（当你知道窗口已关闭时调用）"""
    global _cached_capture, _cached_ocr
    logger.info("重置缓存")
    _cached_capture = None
    _cached_ocr = None

# ==========================================
# 你的原始代码的直接替换版本
# ==========================================

def your_original_code_fixed():
    """
    这是你原始代码的直接替换版本：
    
    原始代码：
    sandboxie_capture = IdentifySandboxieCapture()
    ocr_controller = OCRController(gc=sandboxie_capture)
    ocr_controller.find_text_and_bg_click("沙箱", match_all=False, mss_mode=False)
    
    替换为：
    """
    sandboxie_capture = get_sandboxie_capture()  # 自动处理句柄失效
    ocr_controller = get_ocr_controller(gc=sandboxie_capture)  # 自动处理句柄失效
    
    # 使用OCR查找并点击"沙箱"文字
    return ocr_controller.find_text_and_bg_click("沙箱", match_all=False, mss_mode=False)

# ==========================================
# 更简洁的一行式调用
# ==========================================

def click_sandbox_text():
    """一行式调用，完全隐藏复杂性"""
    return get_ocr_controller().find_text_and_bg_click("沙箱", match_all=False, mss_mode=False)

# ==========================================
# 使用示例和测试
# ==========================================

if __name__ == "__main__":
    import time
    
    print("=== 简单句柄修复方案测试 ===")
    
    # 方式1：直接替换你的原始代码
    print("\n1. 原始代码替换版本:")
    result1 = your_original_code_fixed()
    print(f"结果: {result1}")
    
    # 方式2：一行式调用
    print("\n2. 一行式调用:")
    result2 = click_sandbox_text()
    print(f"结果: {result2}")
    
    # 测试多次调用（验证缓存和句柄刷新）
    print("\n3. 多次调用测试:")
    for i in range(3):
        print(f"第{i+1}次调用:")
        
        # 获取实例
        capture = get_sandboxie_capture()
        ocr = get_ocr_controller()
        
        print(f"  句柄: {capture.hwnd}")
        print(f"  capture对象ID: {id(capture)}")
        print(f"  ocr对象ID: {id(ocr)}")
        
        # 执行操作
        success = ocr.find_text_and_bg_click("沙箱", match_all=False, mss_mode=False)
        print(f"  操作结果: {success}")
        
        time.sleep(1)
    
    print("\n测试完成！")
    print("\n总结：")
    print("- 实例被缓存和重用")
    print("- 句柄自动验证和刷新") 
    print("- 使用方式与原始代码几乎相同")
