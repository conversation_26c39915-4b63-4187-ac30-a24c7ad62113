"""
简化解决方案：直接在你的原始代码基础上改进
无需复杂的管理器，只需要简单的句柄验证机制
"""
from automation.capture.identify_sandboxie_capture import IdentifySandboxieCapture
from automation.controller.OCRController import OCRController
from mylogger.MyLogger import MyLogger

logger = MyLogger('SimpleSolution')

# 全局实例缓存
_sandboxie_capture = None
_ocr_controller = None

def get_sandboxie_instances():
    """
    获取Sandboxie相关实例，自动处理句柄失效问题
    返回: (capture, ocr_controller)
    """
    global _sandboxie_capture, _ocr_controller
    
    try:
        # 如果是第一次调用，创建实例
        if _sandboxie_capture is None:
            logger.info("首次创建Sandboxie实例")
            _sandboxie_capture = IdentifySandboxieCapture()
            _ocr_controller = OCRController(gc=_sandboxie_capture)
        else:
            # 验证现有实例的窗口句柄是否仍然有效
            if hasattr(_sandboxie_capture, '_validate_hwnd'):
                if not _sandboxie_capture._validate_hwnd():
                    logger.warning("窗口句柄验证失败，重新创建实例")
                    _sandboxie_capture = IdentifySandboxieCapture()
                    _ocr_controller = OCRController(gc=_sandboxie_capture)
        
        return _sandboxie_capture, _ocr_controller
        
    except Exception as e:
        logger.error(f"获取Sandboxie实例失败: {e}")
        # 出错时重新创建
        _sandboxie_capture = IdentifySandboxieCapture()
        _ocr_controller = OCRController(gc=_sandboxie_capture)
        return _sandboxie_capture, _ocr_controller

def click_sandbox_text():
    """你的原始代码的改进版本"""
    try:
        # 获取实例（自动处理句柄失效）
        sandboxie_capture, ocr_controller = get_sandboxie_instances()
        
        # 使用OCR查找并点击"沙箱"文字
        success = ocr_controller.find_text_and_bg_click("沙箱", match_all=False, mss_mode=False)
        
        if success:
            logger.info("成功点击沙箱文本")
        else:
            logger.warning("未找到沙箱文本")
            
        return success
        
    except Exception as e:
        logger.error(f"点击沙箱文本失败: {e}")
        return False

def reset_instances():
    """重置实例缓存（当你知道窗口已经关闭时调用）"""
    global _sandboxie_capture, _ocr_controller
    logger.info("重置Sandboxie实例缓存")
    _sandboxie_capture = None
    _ocr_controller = None

# 使用示例
if __name__ == "__main__":
    # 第一次调用
    print("第一次调用:")
    click_sandbox_text()
    
    # 模拟窗口关闭后重新打开
    print("\n模拟窗口重新打开:")
    reset_instances()  # 可选：手动重置
    click_sandbox_text()
    
    # 多次调用测试
    print("\n多次调用测试:")
    for i in range(3):
        print(f"第{i+1}次调用:")
        click_sandbox_text()
