"""
句柄刷新解决方案
专注解决句柄失效问题，保持实例不变，只刷新句柄
"""
import sys
import os
import win32gui

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from automation.capture.identify_sandboxie_capture import IdentifySandboxieCapture
from automation.controller.OCRController import OCRController
from mylogger.MyLogger import MyLogger

logger = MyLogger('HandleRefreshSolution')

# 全局单例实例
_sandboxie_capture = None
_ocr_controller = None

def _is_handle_valid(capture):
    """检查窗口句柄是否有效"""
    if not capture or not capture.hwnd:
        return False
    
    try:
        hwnd = capture.hwnd
        
        # 检查句柄是否仍然是有效窗口
        if not win32gui.IsWindow(hwnd):
            return False
        
        # 检查窗口是否可见
        if not win32gui.IsWindowVisible(hwnd):
            return False
        
        # 检查窗口标题
        title = win32gui.GetWindowText(hwnd)
        if "sandboxie" not in title.lower():
            return False
        
        return True
        
    except Exception as e:
        logger.debug(f"句柄验证失败: {e}")
        return False

def _refresh_handle(capture):
    """刷新窗口句柄"""
    try:
        old_hwnd = capture.hwnd
        logger.info(f"刷新句柄，当前句柄: {old_hwnd}")
        
        # 调用_update_rect来重新获取窗口句柄
        capture._update_rect()
        
        new_hwnd = capture.hwnd
        if new_hwnd:
            if new_hwnd != old_hwnd:
                logger.info(f"句柄已更新: {old_hwnd} -> {new_hwnd}")
            else:
                logger.info(f"句柄刷新完成，句柄未变: {new_hwnd}")
            return True
        else:
            logger.error("句柄刷新失败，未找到有效窗口")
            return False
            
    except Exception as e:
        logger.error(f"刷新句柄异常: {e}")
        return False

def get_sandboxie_instances():
    """
    获取Sandboxie实例，自动处理句柄失效问题
    返回: (capture, ocr_controller)
    """
    global _sandboxie_capture, _ocr_controller
    
    # 第一次创建实例
    if _sandboxie_capture is None:
        logger.info("首次创建Sandboxie实例")
        _sandboxie_capture = IdentifySandboxieCapture()
        _ocr_controller = OCRController(gc=_sandboxie_capture)
        logger.info(f"实例创建完成，初始句柄: {_sandboxie_capture.hwnd}")
        return _sandboxie_capture, _ocr_controller
    
    # 检查现有句柄是否有效
    if _is_handle_valid(_sandboxie_capture):
        logger.debug("现有句柄仍然有效")
        return _sandboxie_capture, _ocr_controller
    
    # 句柄无效，尝试刷新
    logger.warning("检测到句柄失效，尝试刷新...")
    if _refresh_handle(_sandboxie_capture):
        logger.info("句柄刷新成功")
        # 确保OCR控制器引用的是同一个capture对象
        _ocr_controller.gc = _sandboxie_capture
        return _sandboxie_capture, _ocr_controller
    else:
        logger.error("句柄刷新失败，重新创建实例")
        # 只有在刷新失败时才重新创建
        _sandboxie_capture = IdentifySandboxieCapture()
        _ocr_controller = OCRController(gc=_sandboxie_capture)
        return _sandboxie_capture, _ocr_controller

def click_sandbox_text_with_handle_refresh():
    """
    你的原始代码的改进版本 - 专注于句柄刷新
    """
    try:
        # 获取实例（自动处理句柄刷新）
        sandboxie_capture, ocr_controller = get_sandboxie_instances()
        
        logger.info(f"使用句柄 {sandboxie_capture.hwnd} 执行OCR操作")
        
        # 使用OCR查找并点击"沙箱"文字
        success = ocr_controller.find_text_and_bg_click("沙箱", match_all=False, mss_mode=False)
        
        if success:
            logger.info("成功点击沙箱文本")
        else:
            logger.warning("未找到沙箱文本")
        
        return success
        
    except Exception as e:
        logger.error(f"操作失败: {e}")
        return False

def reset_instances():
    """重置实例（当你确定窗口已关闭时可以调用）"""
    global _sandboxie_capture, _ocr_controller
    logger.info("重置Sandboxie实例")
    _sandboxie_capture = None
    _ocr_controller = None

def get_current_handle_info():
    """获取当前句柄信息（用于调试）"""
    global _sandboxie_capture
    if _sandboxie_capture:
        try:
            hwnd = _sandboxie_capture.hwnd
            if hwnd:
                title = win32gui.GetWindowText(hwnd)
                is_valid = win32gui.IsWindow(hwnd)
                is_visible = win32gui.IsWindowVisible(hwnd)
                
                return {
                    'hwnd': hwnd,
                    'title': title,
                    'is_valid': is_valid,
                    'is_visible': is_visible,
                    'handle_check': _is_handle_valid(_sandboxie_capture)
                }
        except Exception as e:
            return {'error': str(e)}
    return {'status': 'no_instance'}

# 使用示例
if __name__ == "__main__":
    import time
    
    print("=== 句柄刷新解决方案测试 ===")
    
    # 第一次调用
    print("\n1. 第一次调用:")
    success1 = click_sandbox_text_with_handle_refresh()
    info1 = get_current_handle_info()
    print(f"结果: {success1}, 句柄信息: {info1}")
    
    # 等待一段时间
    time.sleep(2)
    
    # 第二次调用（模拟句柄可能失效的情况）
    print("\n2. 第二次调用:")
    success2 = click_sandbox_text_with_handle_refresh()
    info2 = get_current_handle_info()
    print(f"结果: {success2}, 句柄信息: {info2}")
    
    # 第三次调用
    print("\n3. 第三次调用:")
    success3 = click_sandbox_text_with_handle_refresh()
    info3 = get_current_handle_info()
    print(f"结果: {success3}, 句柄信息: {info3}")
    
    # 检查实例是否保持不变
    capture1, ocr1 = get_sandboxie_instances()
    capture2, ocr2 = get_sandboxie_instances()
    
    print(f"\n实例保持检查:")
    print(f"capture对象相同: {capture1 is capture2}")
    print(f"ocr对象相同: {ocr1 is ocr2}")
    print(f"当前句柄: {capture1.hwnd}")
    
    print("\n测试完成！")
