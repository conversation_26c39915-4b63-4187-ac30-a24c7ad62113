import threading
import numpy as np
# ModuleNotFoundError: No module named 'win32.distutils.command'
# pip install pywin32 instead win32gui
import win32gui, win32ui, win32con
import math
from ctypes import windll
from mylogger.MyLogger import MyLogger
from mss import mss

logger = MyLogger('window_capture')

# https://www.youtube.com/watch?v=WymCpVUPWQ4
# https://github.com/learncodebygaming/opencv_tutorials/blob/master/004_window_capture/windowcapture.py
class WindowsNotFoundException(Exception):
    def __init__(self, windows_name):
        super(WindowsNotFoundException, self).__init__(f"没有找到名称为'{windows_name}'的窗口!")


class WindowCapture:
    """
    窗口捕获器，通过传入窗口的名称获取截图
    """

    # properties
    w = 1920
    h = 1080
    hwnd = None
    cropped_x = 0
    cropped_y = 0
    offset_x = 0
    offset_y = 0

    # constructor
    def __init__(self, window_name='Elsword'):
        # DPI不是100%的时候，需要调用下面的方法正确才能获取窗口大小
        # https://stackoverflow.com/questions/40869982/dpi-scaling-level-affecting-win32gui-getwindowrect-in-python/45911849
        # Make program aware of DPI scaling
        user32 = windll.user32
        user32.SetProcessDPIAware()
        self.lock = threading.Lock()
        self.window_name = window_name

        # 创建全黑图片
        black_image = np.zeros((self.h, self.w, 4), dtype=np.uint8)
        # 设置alpha通道为255（完全不透明）
        black_image[:, :, 3] = 255
        self.last_screen = black_image

    def get_screen_scale_factor(self):
        """
        获取屏幕缩放
        :return:
        """
        user32 = windll.user32
        user32.SetProcessDPIAware()
        

        dpi = user32.GetDpiForWindow(user32.GetForegroundWindow())
        scale_factor = dpi / 96  # 96 DPI is the standard DPI for 100% scaling

        return scale_factor * 100

    def activate_window(self):
        """
        将窗口置于前台
        :return:
        """
        # 改为通用的窗口查找方式，不限定UnityWndClass
        self.hwnd = win32gui.FindWindow(None, self.window_name)
        if not self.hwnd:
            raise WindowsNotFoundException(self.window_name)
        
        # self.hwnd = win32gui.FindWindow("UnityWndClass", self.window_name)
        # if not self.hwnd:
        #     raise WindowsNotFoundException(self.window_name)

        if self.hwnd is not None:
            win32gui.SetForegroundWindow(self.hwnd)

    def is_active(self):
        """
        判断窗口是否处于前台
        :return:
        """
        current_win_name = win32gui.GetWindowText(win32gui.GetForegroundWindow())
        act = current_win_name == self.window_name
        if not act: logger.debug(f'当前窗口名称：{current_win_name}')
        return act

    def _update_rect(self):
        # 使用增强的窗口查找方法，支持模糊匹配
        self.hwnd = self._get_window_handle()
        if not self.hwnd:
            raise WindowsNotFoundException(self.window_name)

        # 获取窗口的完整矩形区域和客户区矩形区域
        window_rect = win32gui.GetWindowRect(self.hwnd)
        client_rect = win32gui.GetClientRect(self.hwnd)
        old_w = self.w
        old_h = self.h

        # 计算完整窗口的实际像素尺寸
        window_w = window_rect[2] - window_rect[0]
        window_h = window_rect[3] - window_rect[1]

        # 计算窗口边框和标题栏的像素大小
        border_pixels = math.floor((window_w - client_rect[2]) / 2)
        titlebar_pixels = window_h - client_rect[3] - border_pixels

        # 设置实际截图区域的尺寸（去除边框和标题栏）
        self.w = window_w - (border_pixels * 2)
        self.h = window_h - titlebar_pixels - border_pixels
        self.cropped_x = border_pixels
        self.cropped_y = titlebar_pixels

        # 设置坐标偏移量
        self.offset_x = window_rect[0] + self.cropped_x
        self.offset_y = window_rect[1] + self.cropped_y

        # 检测分辨率是否发生变化
        if client_rect[2] != old_w or client_rect[3] != old_h:
            logger.info(f'Resolution changed to {self.w}*{self.h}')
            if hasattr(self, 'notice_update_event'):
                self.notice_update_event()

    def _validate_hwnd(self):
        """验证窗口句柄是否仍然有效"""
        try:
            if not self.hwnd:
                logger.debug("窗口句柄为空，需要重新获取")
                self._update_rect()
                return self.hwnd is not None

            # 1. 检查句柄是否仍然是有效的窗口
            if not win32gui.IsWindow(self.hwnd):
                logger.warning(f"窗口句柄{self.hwnd}已失效（窗口不存在）")
                self._update_rect()
                return self.hwnd is not None

            # 2. 检查窗口是否可见
            if not win32gui.IsWindowVisible(self.hwnd):
                logger.warning(f"窗口句柄{self.hwnd}对应的窗口不可见")
                self._update_rect()
                return self.hwnd is not None

            # 3. 检查窗口标题是否匹配
            try:
                current_title = win32gui.GetWindowText(self.hwnd)
                if self.window_name not in current_title:
                    logger.warning(f"窗口标题不匹配: 期望包含'{self.window_name}', 实际'{current_title}'")
                    self._update_rect()
                    return self.hwnd is not None
            except:
                logger.warning("无法获取窗口标题，可能窗口已关闭")
                self._update_rect()
                return self.hwnd is not None

            # 4. 尝试获取窗口矩形，验证窗口是否可访问
            try:
                rect = win32gui.GetWindowRect(self.hwnd)
                if rect[2] - rect[0] <= 0 or rect[3] - rect[1] <= 0:
                    logger.warning("窗口矩形无效，可能窗口已最小化或隐藏")
                    self._update_rect()
                    return self.hwnd is not None
            except:
                logger.warning("无法获取窗口矩形，窗口可能已关闭")
                self._update_rect()
                return self.hwnd is not None

            # 5. 所有检查都通过
            logger.debug(f"窗口句柄{self.hwnd}验证通过")
            return True

        except Exception as e:
            logger.error(f"验证窗口句柄失败: {e}")
            # 出错时重新获取句柄
            try:
                self._update_rect()
                return self.hwnd is not None
            except:
                return False

    def get_screenshot(self, use_alpha=True, mss_mode=False):
        """
        :param use_alpha:
        :param mss_mode: 裁剪屏幕的方式而非直接获取游戏窗口。登录界面只能通过这种方式截图到登录框
        :return:
        """
        # 在截图前验证窗口句柄
        if not self._validate_hwnd():
            logger.error("无法获取有效的窗口句柄")
            return self.last_screen

        if mss_mode: img = self.__get_screenshot_mss()
        else: img = self.__get_screenshot_alpha()

        if use_alpha:
            return img

        img = img[..., :3]

        # make image C_CONTIGUOUS to avoid errors that look like:
        #   File ... in draw_rectangles
        #   TypeError: an integer is required (got type tuple)
        # see the discussion here:
        # https://github.com/opencv/opencv/issues/14866#issuecomment-580207109
        img = np.ascontiguousarray(img)
        return img

    def __get_screenshot_alpha(self):
        # get the window image data
        try:
            with self.lock:
                wDC = win32gui.GetWindowDC(self.hwnd)
                dcObj = win32ui.CreateDCFromHandle(wDC)
                cDC = dcObj.CreateCompatibleDC()
                assert self.w > 0 and self.h > 0, f"Invalid dimensions: width={self.w}, height={self.h}"

                dataBitMap = win32ui.CreateBitmap()
                dataBitMap.CreateCompatibleBitmap(dcObj, self.w, self.h)
                cDC.SelectObject(dataBitMap)
                result = cDC.BitBlt((0, 0), (self.w, self.h), dcObj, (self.cropped_x, self.cropped_y), win32con.SRCCOPY)
                assert result != 0, "BitBlt failed, check dimensions and coordinates"
                # convert the raw data into a format opencv can read
                # dataBitMap.SaveBitmapFile(cDC, 'debug.bmp')
                signedIntsArray = dataBitMap.GetBitmapBits(True)
                # img = np.fromstring(signedIntsArray, dtype='uint8')
                img = np.frombuffer(signedIntsArray, dtype='uint8')
                img.shape = (self.h, self.w, 4)

                # free resources
                dcObj.DeleteDC()
                cDC.DeleteDC()
                win32gui.ReleaseDC(self.hwnd, wDC)
                win32gui.DeleteObject(dataBitMap.GetHandle())
                self.last_screen = img
        except Exception as e:
            logger.error(e)
            # 不知道如何解决
            # dataBitMap.CreateCompatibleBitmap(dcObj, self.w, self.h)
            # win32ui.error: CreateCompatibleDC failed
            return self.last_screen
        return img


    def __get_screenshot_mss(self):
        """
        可以截图该区域上的任意窗口
        :return:
        """
        # 获取窗口位置和大小
        # 获取窗口客户区的矩形
        rect = win32gui.GetClientRect(self.hwnd)
        # 将客户区坐标转换为屏幕坐标
        x, y = win32gui.ClientToScreen(self.hwnd, (0, 0))
        w, h = rect[2], rect[3]

        # 创建 mss 实例
        with mss() as sct:
            # 定义捕获区域
            monitor = {"top": y, "left": x, "width": w, "height": h}

            # 捕获屏幕
            screenshot = sct.grab(monitor)

            # 转换为 numpy 数组
            img = np.array(screenshot)

            return img

    # find the name of the window you're interested in.
    # once you have it, update window_capture()
    # https://stackoverflow.com/questions/55547940/how-to-get-a-list-of-the-name-of-every-open-window
    def list_window_names(self):
        def winEnumHandler(hwnd, ctx):
            if win32gui.IsWindowVisible(hwnd):
                print(hex(hwnd), win32gui.GetWindowText(hwnd))

        win32gui.EnumWindows(winEnumHandler, None)

    # translate a pixel position on a screenshot image to a pixel position on the screen.
    # pos = (x, y)
    # WARNING: if you move the window being captured after execution is started, this will
    # return incorrect coordinates, because the window position is only calculated in
    # the __init__ constructor.
    def get_screen_position(self, game_pos):
        """
        将游戏内坐标转换为屏幕坐标
        """
        return (game_pos[0] + self.offset_x, game_pos[1] + self.offset_y)

    def get_window_center(self):
        """
        获取窗口中心在屏幕中的实际位置
        :return: (x, y) 屏幕坐标
        """
        return self.get_screen_position((self.w / 2, self.h / 2))

    def get_window_center_game_pos(self):
        """
        获取窗口中心的游戏内坐标
        :return: (x, y) 游戏内坐标
        """
        return (self.w / 2, self.h / 2)

    def notice_update_event(self):
        """
        分辨率改变时调用，子类实现
        :return:
        """
        pass

    def _find_window_fuzzy(self, partial_title):
        """模糊查找窗口句柄"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if window_text and partial_title.lower() in window_text.lower():
                    windows.append((hwnd, window_text))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            logger.info(f"找到 {len(windows)} 个匹配窗口:")
            for hwnd, title in windows:
                logger.info(f"  句柄: {hwnd}, 标题: {title}")
            return windows[0][0]
        
        return None

    def _get_window_handle(self):
        """获取窗口句柄，支持精确和模糊匹配，优先选择最合适的窗口"""
        # 首先尝试精确匹配
        hwnd = win32gui.FindWindow(None, self.window_name)
        if hwnd and self._is_valid_target_window(hwnd):
            logger.debug(f"精确匹配找到窗口: {hwnd}")
            return hwnd

        # 精确匹配失败，尝试模糊匹配
        logger.warning(f"精确匹配失败，尝试模糊匹配: {self.window_name}")
        hwnd = self._find_best_matching_window(self.window_name)
        if hwnd:
            logger.info(f"模糊匹配找到窗口: {hwnd}")
            return hwnd

        return None

    def _is_valid_target_window(self, hwnd):
        """检查窗口是否是有效的目标窗口"""
        try:
            # 检查窗口是否可见
            if not win32gui.IsWindowVisible(hwnd):
                return False

            # 检查窗口是否有有效的矩形
            rect = win32gui.GetWindowRect(hwnd)
            if rect[2] - rect[0] <= 0 or rect[3] - rect[1] <= 0:
                return False

            # 检查窗口是否不是最小化状态
            placement = win32gui.GetWindowPlacement(hwnd)
            if placement[1] == 2:  # SW_SHOWMINIMIZED
                return False

            return True
        except:
            return False

    def _find_best_matching_window(self, partial_title):
        """找到最佳匹配的窗口，优先选择前台、可见、大小合适的窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                try:
                    window_text = win32gui.GetWindowText(hwnd)
                    if window_text and partial_title.lower() in window_text.lower():
                        # 获取窗口信息用于排序
                        rect = win32gui.GetWindowRect(hwnd)
                        width = rect[2] - rect[0]
                        height = rect[3] - rect[1]

                        # 检查是否是前台窗口
                        is_foreground = hwnd == win32gui.GetForegroundWindow()

                        # 检查窗口状态
                        placement = win32gui.GetWindowPlacement(hwnd)
                        is_normal = placement[1] == 1  # SW_SHOWNORMAL

                        windows.append({
                            'hwnd': hwnd,
                            'title': window_text,
                            'width': width,
                            'height': height,
                            'is_foreground': is_foreground,
                            'is_normal': is_normal,
                            'area': width * height
                        })
                except:
                    pass
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)

        if not windows:
            return None

        # 按优先级排序：前台窗口 > 正常状态 > 面积大的窗口
        windows.sort(key=lambda w: (
            w['is_foreground'],
            w['is_normal'],
            w['area']
        ), reverse=True)

        logger.info(f"找到 {len(windows)} 个匹配窗口:")
        for i, w in enumerate(windows[:3]):  # 只显示前3个
            logger.info(f"  {i+1}. 句柄:{w['hwnd']}, 标题:{w['title']}, "
                       f"前台:{w['is_foreground']}, 正常:{w['is_normal']}, "
                       f"大小:{w['width']}x{w['height']}")

        # 返回最佳匹配的窗口
        best_window = windows[0]
        logger.info(f"选择窗口: {best_window['hwnd']} - {best_window['title']}")
        return best_window['hwnd']

    def _list_all_windows(self):
        """列出所有可见窗口，用于调试"""
        def enum_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if window_text:  # 只显示有标题的窗口
                    windows.append(f"句柄: {hwnd}, 标题: {window_text}")
            return True
        
        windows = []
        win32gui.EnumWindows(enum_callback, windows)
        
        logger.info("当前所有可见窗口:")
        for window in windows[:20]:  # 只显示前20个
            logger.info(f"  {window}")


if __name__ == '__main__':
    # windows_name = get_config('window_name')
    wc = WindowCapture()
    import time
    import cv2 as cv

    if not wc.is_active():
        wc.activate_window()

    sc = wc.get_screenshot(mss_mode=False)
    # cv2.imwrite('screenshot1.jpg', sc)
    # sys.exit(0)
    loop_time = time.time()
    while True:
        t = time.time()
        sc = wc.get_screenshot(mss_mode=True)
        # print(win32gui.GetWindowText(wc.hwnd))
        # print(wc.is_active(),time.time()-t)
        # cv.namedWindow('window capture', cv.WINDOW_GUI_EXPANDED)
        # cv.imshow('window capture', sc)
        print(time.time()-t)
        # cost_time = time.time() - loop_time
        # print("fps:", 1 / cost_time, 'cost time:', cost_time)
        # loop_time = time.time()
        # key = cv.waitKey(1)
        # if key & 0xFF == ord('q'): break
    # cv.destroyAllWindows()





