"""
高级窗口句柄验证示例
展示改进后的句柄验证机制如何处理各种复杂情况
"""
import time
import win32gui
from automation.capture.identify_sandboxie_capture import IdentifySandboxieCapture
from automation.controller.OCRController import OCRController
from mylogger.MyLogger import MyLogger

logger = MyLogger('AdvancedHandleValidation')

class SmartSandboxieManager:
    """智能Sandboxie管理器，处理各种窗口状态变化"""
    
    def __init__(self):
        self.capture = None
        self.ocr_controller = None
        self.last_hwnd = None
        self.validation_count = 0
        
    def get_instances(self):
        """获取实例，自动处理各种窗口状态变化"""
        try:
            # 第一次创建或需要重新创建
            if self.capture is None:
                logger.info("创建新的Sandboxie实例")
                self.capture = IdentifySandboxieCapture()
                self.ocr_controller = OCRController(gc=self.capture)
                self.last_hwnd = self.capture.hwnd
                return self.capture, self.ocr_controller
            
            # 验证现有实例
            self.validation_count += 1
            logger.info(f"第{self.validation_count}次验证窗口句柄")
            
            # 检查句柄是否发生变化
            current_hwnd = self.capture.hwnd
            if current_hwnd != self.last_hwnd:
                logger.warning(f"窗口句柄已变化: {self.last_hwnd} -> {current_hwnd}")
                self.last_hwnd = current_hwnd
                
                # 如果句柄变化，重新创建OCR控制器
                if current_hwnd:
                    logger.info("重新创建OCR控制器")
                    self.ocr_controller = OCRController(gc=self.capture)
            
            # 如果验证失败，重新创建所有实例
            if not self.capture.hwnd:
                logger.error("窗口句柄验证失败，重新创建所有实例")
                self.capture = IdentifySandboxieCapture()
                self.ocr_controller = OCRController(gc=self.capture)
                self.last_hwnd = self.capture.hwnd
            
            return self.capture, self.ocr_controller
            
        except Exception as e:
            logger.error(f"获取实例失败: {e}")
            # 强制重新创建
            self.capture = IdentifySandboxieCapture()
            self.ocr_controller = OCRController(gc=self.capture)
            self.last_hwnd = self.capture.hwnd
            return self.capture, self.ocr_controller
    
    def click_text(self, text, **kwargs):
        """点击文本，自动处理窗口状态变化"""
        try:
            capture, ocr = self.get_instances()
            
            if not capture.hwnd:
                logger.error("无法获取有效的窗口句柄")
                return False
            
            logger.info(f"使用窗口句柄 {capture.hwnd} 查找文本: {text}")
            success = ocr.find_text_and_bg_click(text, **kwargs)
            
            if success:
                logger.info(f"成功点击文本: {text}")
            else:
                logger.warning(f"未找到文本: {text}")
            
            return success
            
        except Exception as e:
            logger.error(f"点击文本失败 {text}: {e}")
            return False
    
    def get_window_info(self):
        """获取当前窗口信息"""
        try:
            if self.capture and self.capture.hwnd:
                hwnd = self.capture.hwnd
                title = win32gui.GetWindowText(hwnd)
                rect = win32gui.GetWindowRect(hwnd)
                is_visible = win32gui.IsWindowVisible(hwnd)
                is_foreground = hwnd == win32gui.GetForegroundWindow()
                
                return {
                    'hwnd': hwnd,
                    'title': title,
                    'rect': rect,
                    'is_visible': is_visible,
                    'is_foreground': is_foreground,
                    'width': rect[2] - rect[0],
                    'height': rect[3] - rect[1]
                }
            return None
        except Exception as e:
            logger.error(f"获取窗口信息失败: {e}")
            return None
    
    def reset(self):
        """重置所有实例"""
        logger.info("重置Sandboxie管理器")
        self.capture = None
        self.ocr_controller = None
        self.last_hwnd = None
        self.validation_count = 0

def test_various_scenarios():
    """测试各种窗口状态变化场景"""
    manager = SmartSandboxieManager()
    
    scenarios = [
        ("沙箱", {"match_all": False, "mss_mode": False}),
        ("创建", {"match_all": False, "mss_mode": False}),
        ("启动", {"match_all": False, "mss_mode": False}),
    ]
    
    for i, (text, kwargs) in enumerate(scenarios):
        logger.info(f"\n=== 场景 {i+1}: 查找文本 '{text}' ===")
        
        # 显示当前窗口信息
        window_info = manager.get_window_info()
        if window_info:
            logger.info(f"当前窗口: {window_info['hwnd']} - {window_info['title']}")
            logger.info(f"窗口大小: {window_info['width']}x{window_info['height']}")
            logger.info(f"可见: {window_info['is_visible']}, 前台: {window_info['is_foreground']}")
        else:
            logger.info("当前无有效窗口")
        
        # 执行操作
        success = manager.click_text(text, **kwargs)
        logger.info(f"操作结果: {'成功' if success else '失败'}")
        
        # 等待一段时间，模拟实际使用
        time.sleep(1)
    
    # 显示最终统计
    logger.info(f"\n=== 测试完成 ===")
    logger.info(f"总验证次数: {manager.validation_count}")
    final_info = manager.get_window_info()
    if final_info:
        logger.info(f"最终窗口句柄: {final_info['hwnd']}")

def test_handle_changes():
    """测试句柄变化检测"""
    manager = SmartSandboxieManager()
    
    logger.info("=== 测试句柄变化检测 ===")
    
    # 第一次获取
    capture1, ocr1 = manager.get_instances()
    hwnd1 = capture1.hwnd
    logger.info(f"第一次获取句柄: {hwnd1}")
    
    # 模拟多次调用
    for i in range(3):
        logger.info(f"\n--- 第{i+2}次调用 ---")
        capture2, ocr2 = manager.get_instances()
        hwnd2 = capture2.hwnd
        
        logger.info(f"句柄: {hwnd2}")
        logger.info(f"capture对象相同: {capture1 is capture2}")
        logger.info(f"ocr对象相同: {ocr1 is ocr2}")
        logger.info(f"句柄相同: {hwnd1 == hwnd2}")
        
        time.sleep(0.5)

if __name__ == "__main__":
    print("1. 测试各种场景")
    test_various_scenarios()
    
    print("\n" + "="*50)
    print("2. 测试句柄变化检测")
    test_handle_changes()
    
    print("\n测试完成！")
