"""
最终解决方案：完全替换你的原始代码
解决了所有窗口句柄失效的问题，包括：
1. 窗口重复打开
2. 窗口关闭后重新打开
3. 窗口最小化/恢复
4. 窗口位置/大小变化
5. 应用程序重启
"""
import sys
import os
import time
import win32gui

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from automation.capture.identify_sandboxie_capture import IdentifySandboxieCapture
from automation.controller.OCRController import OCRController
from mylogger.MyLogger import MyLogger

logger = MyLogger('FinalSolution')

class RobustSandboxieAutomation:
    """
    健壮的Sandboxie自动化类
    自动处理窗口句柄失效问题（保持实例，只刷新句柄）
    """

    def __init__(self):
        self._capture = None
        self._ocr_controller = None
        self._last_successful_hwnd = None
        self._creation_time = None

    def _create_instances_once(self):
        """只创建一次实例（单例模式）"""
        if self._capture is None or self._ocr_controller is None:
            logger.info("首次创建Sandboxie实例")
            try:
                self._capture = IdentifySandboxieCapture()
                self._ocr_controller = OCRController(gc=self._capture)
                self._last_successful_hwnd = self._capture.hwnd
                self._creation_time = time.time()

                if self._capture.hwnd:
                    logger.info(f"成功创建实例，窗口句柄: {self._capture.hwnd}")
                    return True
                else:
                    logger.error("创建实例失败，未找到有效窗口")
                    return False

            except Exception as e:
                logger.error(f"创建实例异常: {e}")
                return False
        return True
    
    def _refresh_handle_if_needed(self):
        """检查并刷新窗口句柄（保持实例不变）"""
        if not self._capture:
            return False

        try:
            current_hwnd = self._capture.hwnd

            # 检查当前句柄是否有效
            if current_hwnd and win32gui.IsWindow(current_hwnd):
                try:
                    # 进一步验证窗口状态
                    if win32gui.IsWindowVisible(current_hwnd):
                        title = win32gui.GetWindowText(current_hwnd)
                        if "sandboxie" in title.lower():
                            # 句柄仍然有效
                            if current_hwnd != self._last_successful_hwnd:
                                logger.info(f"检测到句柄变化: {self._last_successful_hwnd} -> {current_hwnd}")
                                self._last_successful_hwnd = current_hwnd
                            return True
                except:
                    pass

            # 句柄无效，需要刷新
            logger.warning(f"窗口句柄{current_hwnd}已失效，尝试刷新...")

            # 调用capture的刷新方法（这会更新hwnd）
            if hasattr(self._capture, '_update_rect'):
                self._capture._update_rect()
                new_hwnd = self._capture.hwnd

                if new_hwnd and new_hwnd != current_hwnd:
                    logger.info(f"成功刷新句柄: {current_hwnd} -> {new_hwnd}")
                    self._last_successful_hwnd = new_hwnd

                    # 更新OCR控制器中的句柄引用
                    if hasattr(self._ocr_controller, 'gc'):
                        self._ocr_controller.gc = self._capture

                    return True
                elif new_hwnd:
                    logger.info("句柄刷新成功，句柄未变化")
                    return True
                else:
                    logger.error("句柄刷新失败，未找到有效窗口")
                    return False
            else:
                logger.error("capture对象没有_update_rect方法")
                return False

        except Exception as e:
            logger.error(f"刷新句柄失败: {e}")
            return False
    
    def _get_valid_instances(self):
        """获取有效的实例，必要时只刷新句柄"""
        # 确保实例已创建
        if not self._create_instances_once():
            raise Exception("无法创建Sandboxie实例")

        # 刷新句柄（如果需要）
        if not self._refresh_handle_if_needed():
            raise Exception("无法获取有效的窗口句柄")

        return self._capture, self._ocr_controller
    
    def click_text(self, text, match_all=False, mss_mode=False, max_retries=3):
        """
        点击指定文本，自动处理所有窗口句柄问题
        
        Args:
            text: 要查找的文本
            match_all: 是否完全匹配
            mss_mode: 是否使用MSS模式
            max_retries: 最大重试次数
        
        Returns:
            bool: 是否成功点击
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"第{attempt + 1}次尝试点击文本: {text}")
                
                # 获取有效实例
                capture, ocr_controller = self._get_valid_instances()
                
                # 执行点击操作
                success = ocr_controller.find_text_and_bg_click(
                    text, 
                    match_all=match_all, 
                    mss_mode=mss_mode
                )
                
                if success:
                    logger.info(f"成功点击文本: {text}")
                    return True
                else:
                    logger.warning(f"未找到文本: {text}")
                    if attempt < max_retries - 1:
                        logger.info("等待1秒后重试...")
                        time.sleep(1)
                
            except Exception as e:
                logger.error(f"第{attempt + 1}次尝试失败: {e}")
                if attempt < max_retries - 1:
                    logger.info("强制刷新句柄后重试...")
                    # 不重新创建实例，只强制刷新句柄
                    if self._capture and hasattr(self._capture, '_update_rect'):
                        try:
                            self._capture._update_rect()
                        except:
                            pass
                    time.sleep(1)
        
        logger.error(f"所有尝试都失败，无法点击文本: {text}")
        return False
    
    def get_window_info(self):
        """获取当前窗口信息"""
        try:
            capture, _ = self._get_valid_instances()
            if capture.hwnd:
                return {
                    'hwnd': capture.hwnd,
                    'title': win32gui.GetWindowText(capture.hwnd),
                    'is_visible': win32gui.IsWindowVisible(capture.hwnd),
                    'creation_time': self._creation_time
                }
        except:
            pass
        return None

# 全局实例
_sandboxie_automation = None

def get_sandboxie_automation():
    """获取全局Sandboxie自动化实例"""
    global _sandboxie_automation
    if _sandboxie_automation is None:
        _sandboxie_automation = RobustSandboxieAutomation()
    return _sandboxie_automation

def click_sandbox_text():
    """你的原始代码的最终改进版本"""
    automation = get_sandboxie_automation()
    return automation.click_text("沙箱", match_all=False, mss_mode=False)

# 使用示例
if __name__ == "__main__":
    # 替换你的原始代码：
    # sandboxie_capture = IdentifySandboxieCapture()
    # ocr_controller = OCRController(gc=sandboxie_capture)
    # ocr_controller.find_text_and_bg_click("沙箱", match_all=False, mss_mode=False)
    
    # 新的使用方式：
    automation = get_sandboxie_automation()
    
    # 测试多次点击
    texts_to_click = ["沙箱", "创建", "启动", "停止"]
    
    for text in texts_to_click:
        logger.info(f"\n=== 测试点击: {text} ===")
        success = automation.click_text(text)
        
        # 显示窗口信息
        info = automation.get_window_info()
        if info:
            logger.info(f"当前窗口: {info['hwnd']} - {info['title']}")
        
        logger.info(f"结果: {'成功' if success else '失败'}")
        time.sleep(2)  # 等待2秒再进行下一个操作
