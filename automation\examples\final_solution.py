"""
最终解决方案：完全替换你的原始代码
解决了所有窗口句柄失效的问题，包括：
1. 窗口重复打开
2. 窗口关闭后重新打开  
3. 窗口最小化/恢复
4. 窗口位置/大小变化
5. 应用程序重启
"""
import time
import win32gui
from automation.capture.identify_sandboxie_capture import IdentifySandboxieCapture
from automation.controller.OCRController import OCRController
from mylogger.MyLogger import MyLogger

logger = MyLogger('FinalSolution')

class RobustSandboxieAutomation:
    """
    健壮的Sandboxie自动化类
    自动处理所有窗口句柄相关问题
    """
    
    def __init__(self):
        self._capture = None
        self._ocr_controller = None
        self._last_successful_hwnd = None
        self._creation_time = None
        
    def _create_fresh_instances(self):
        """创建全新的实例"""
        logger.info("创建全新的Sandboxie实例")
        try:
            self._capture = IdentifySandboxieCapture()
            self._ocr_controller = OCRController(gc=self._capture)
            self._last_successful_hwnd = self._capture.hwnd
            self._creation_time = time.time()
            
            if self._capture.hwnd:
                logger.info(f"成功创建实例，窗口句柄: {self._capture.hwnd}")
                return True
            else:
                logger.error("创建实例失败，未找到有效窗口")
                return False
                
        except Exception as e:
            logger.error(f"创建实例异常: {e}")
            return False
    
    def _is_instance_valid(self):
        """检查当前实例是否仍然有效"""
        if not self._capture or not self._ocr_controller:
            return False
        
        try:
            # 检查窗口句柄是否仍然有效
            hwnd = self._capture.hwnd
            if not hwnd:
                logger.debug("窗口句柄为空")
                return False
            
            # 检查窗口是否仍然存在
            if not win32gui.IsWindow(hwnd):
                logger.debug(f"窗口句柄{hwnd}已失效")
                return False
            
            # 检查窗口是否可见
            if not win32gui.IsWindowVisible(hwnd):
                logger.debug(f"窗口{hwnd}不可见")
                return False
            
            # 检查窗口标题
            try:
                title = win32gui.GetWindowText(hwnd)
                if "sandboxie" not in title.lower():
                    logger.debug(f"窗口标题不匹配: {title}")
                    return False
            except:
                logger.debug("无法获取窗口标题")
                return False
            
            # 检查窗口矩形
            try:
                rect = win32gui.GetWindowRect(hwnd)
                if rect[2] - rect[0] <= 0 or rect[3] - rect[1] <= 0:
                    logger.debug("窗口矩形无效")
                    return False
            except:
                logger.debug("无法获取窗口矩形")
                return False
            
            # 检查句柄是否发生变化
            if hwnd != self._last_successful_hwnd:
                logger.info(f"窗口句柄已变化: {self._last_successful_hwnd} -> {hwnd}")
                self._last_successful_hwnd = hwnd
                # 句柄变化时重新创建OCR控制器
                self._ocr_controller = OCRController(gc=self._capture)
            
            return True
            
        except Exception as e:
            logger.error(f"验证实例有效性失败: {e}")
            return False
    
    def _get_valid_instances(self):
        """获取有效的实例，必要时重新创建"""
        # 检查现有实例是否有效
        if self._is_instance_valid():
            return self._capture, self._ocr_controller
        
        # 实例无效，重新创建
        logger.info("现有实例无效，重新创建")
        if self._create_fresh_instances():
            return self._capture, self._ocr_controller
        else:
            raise Exception("无法创建有效的Sandboxie实例")
    
    def click_text(self, text, match_all=False, mss_mode=False, max_retries=3):
        """
        点击指定文本，自动处理所有窗口句柄问题
        
        Args:
            text: 要查找的文本
            match_all: 是否完全匹配
            mss_mode: 是否使用MSS模式
            max_retries: 最大重试次数
        
        Returns:
            bool: 是否成功点击
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"第{attempt + 1}次尝试点击文本: {text}")
                
                # 获取有效实例
                capture, ocr_controller = self._get_valid_instances()
                
                # 执行点击操作
                success = ocr_controller.find_text_and_bg_click(
                    text, 
                    match_all=match_all, 
                    mss_mode=mss_mode
                )
                
                if success:
                    logger.info(f"成功点击文本: {text}")
                    return True
                else:
                    logger.warning(f"未找到文本: {text}")
                    if attempt < max_retries - 1:
                        logger.info("等待1秒后重试...")
                        time.sleep(1)
                
            except Exception as e:
                logger.error(f"第{attempt + 1}次尝试失败: {e}")
                if attempt < max_retries - 1:
                    logger.info("强制重新创建实例后重试...")
                    self._capture = None
                    self._ocr_controller = None
                    time.sleep(1)
        
        logger.error(f"所有尝试都失败，无法点击文本: {text}")
        return False
    
    def get_window_info(self):
        """获取当前窗口信息"""
        try:
            capture, _ = self._get_valid_instances()
            if capture.hwnd:
                return {
                    'hwnd': capture.hwnd,
                    'title': win32gui.GetWindowText(capture.hwnd),
                    'is_visible': win32gui.IsWindowVisible(capture.hwnd),
                    'creation_time': self._creation_time
                }
        except:
            pass
        return None

# 全局实例
_sandboxie_automation = None

def get_sandboxie_automation():
    """获取全局Sandboxie自动化实例"""
    global _sandboxie_automation
    if _sandboxie_automation is None:
        _sandboxie_automation = RobustSandboxieAutomation()
    return _sandboxie_automation

def click_sandbox_text():
    """你的原始代码的最终改进版本"""
    automation = get_sandboxie_automation()
    return automation.click_text("沙箱", match_all=False, mss_mode=False)

# 使用示例
if __name__ == "__main__":
    # 替换你的原始代码：
    # sandboxie_capture = IdentifySandboxieCapture()
    # ocr_controller = OCRController(gc=sandboxie_capture)
    # ocr_controller.find_text_and_bg_click("沙箱", match_all=False, mss_mode=False)
    
    # 新的使用方式：
    automation = get_sandboxie_automation()
    
    # 测试多次点击
    texts_to_click = ["沙箱", "创建", "启动", "停止"]
    
    for text in texts_to_click:
        logger.info(f"\n=== 测试点击: {text} ===")
        success = automation.click_text(text)
        
        # 显示窗口信息
        info = automation.get_window_info()
        if info:
            logger.info(f"当前窗口: {info['hwnd']} - {info['title']}")
        
        logger.info(f"结果: {'成功' if success else '失败'}")
        time.sleep(2)  # 等待2秒再进行下一个操作
